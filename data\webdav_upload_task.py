# -*- encoding: utf-8 -*-
'''
@File    :   webdav_upload_task.py
@Time    :   2024-01-15 10:00:00
<AUTHOR>   AI Assistant
@Version :   1.0
@Contact :   
@desc    :   WebDAV上传任务 - 替代aligo的稳定方案
'''

import os
import time
os.chdir("." if os.path.dirname(__file__) == "" else os.path.dirname(__file__))
from utils.log_handler import LOG
from utils.yaml_handler import read_yaml
from utils.file_handler import find_files
from utils.webdav_handler import upload_file


def main(logger, conf_info):
    """主要上传逻辑"""
    
    # WebDAV配置
    webdav_config = conf_info.get("webdav")
    if not webdav_config:
        logger.error(msg="WebDAV配置不存在，请检查config.yaml")
        return
    
    # 上传失败重试次数
    upload_fail_retry_times = webdav_config.get("upload_fail_times", 3)
    # 日志压缩包的路径
    log_rar_path = conf_info.get("log_file").get("rar_path")

    logger.info(msg="WebDAV上传任务开始")

    # 找出生成的所有日志文件
    rar_file_list = [filename for filename in find_files(path=log_rar_path) if filename.endswith(".tgz")]

    if rar_file_list:
        for rar_file_path in rar_file_list:
            logger.info(msg=f"开始上传 {rar_file_path}")
            retry_times = 0
            upload_flag = False
            
            while retry_times < upload_fail_retry_times:
                try:
                    # 使用WebDAV上传
                    upload_res = upload_file(
                        file_path=rar_file_path, 
                        logger=logger, 
                        webdav_config=webdav_config
                    )
                    
                    if upload_res:
                        os.remove(rar_file_path)
                        logger.info(msg=f"压缩包 {rar_file_path} 上传成功，删除本地文件")
                        upload_flag = True
                        break
                    else:
                        logger.error(msg=f"上传失败，第{retry_times + 1}次重试")
                        
                except TimeoutError:
                    logger.error(msg=f"上传超时，第{retry_times + 1}次重试")
                except Exception as e:
                    logger.error(msg=f"上传异常：{e}，第{retry_times + 1}次重试")
                
                retry_times += 1
                if retry_times < upload_fail_retry_times:
                    time.sleep(10)  # 等待10秒后重试
            
            if not upload_flag:
                logger.error(msg=f"日志文件 {rar_file_path} 经过{retry_times}次重试后仍然失败")
                # 可以选择移动到失败目录
                move_to_fail_directory(rar_file_path, conf_info, logger)
    else:
        logger.info(msg="没有找到压缩文件")
    
    logger.info(msg="WebDAV上传任务结束")


def move_to_fail_directory(file_path, conf_info, logger):
    """将失败的文件移动到失败目录"""
    try:
        fail_path = conf_info.get("log_file").get("upload_fail_path", "/tmp/upload_fail")
        if not os.path.exists(fail_path):
            os.makedirs(fail_path, exist_ok=True)
        
        fail_file_path = os.path.join(fail_path, os.path.basename(file_path))
        os.rename(file_path, fail_file_path)
        logger.info(msg=f"失败文件已移动到: {fail_file_path}")
    except Exception as e:
        logger.error(msg=f"移动失败文件时出错: {e}")


def test_webdav_connection(conf_info, logger):
    """测试WebDAV连接"""
    from utils.webdav_handler import WebDAVUploader
    
    webdav_config = conf_info.get("webdav")
    if not webdav_config:
        logger.error(msg="WebDAV配置不存在")
        return False
    
    try:
        uploader = WebDAVUploader(
            webdav_url=webdav_config.get('url'),
            username=webdav_config.get('username'),
            password=webdav_config.get('password'),
            logger=logger
        )
        
        if uploader.check_connection():
            logger.info(msg="WebDAV连接测试成功")
            return True
        else:
            logger.error(msg="WebDAV连接测试失败")
            return False
    except Exception as e:
        logger.error(msg=f"WebDAV连接测试异常: {e}")
        return False


if __name__ == "__main__":
    # 配置文件的路径
    conf_file_path = os.path.join(".", "conf", "config.yaml")
    # 读取配置信息
    conf_info = read_yaml(filename=conf_file_path)
    # 日志保存目录
    out_log_path = conf_info.get("log").get("log_path")
    
    ### 初始化日志对象 BEGIN ###
    log = LOG()
    log.log_to_file(filename=os.path.join(out_log_path, "webdav_upload_task.log"), when="D")
    logger = log.logger
    ### 初始化日志对象 END ###
    
    # 首先测试WebDAV连接
    if test_webdav_connection(conf_info, logger):
        logger.info(msg="WebDAV连接正常，开始上传任务循环")
        while True:
            main(logger, conf_info)
            time.sleep(60)  # 每分钟检查一次
    else:
        logger.error(msg="WebDAV连接失败，请检查配置和Alist服务状态")
