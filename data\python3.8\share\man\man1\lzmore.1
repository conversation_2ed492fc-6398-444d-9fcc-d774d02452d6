.\"
.\" Original zdiff.1 for gzip: <PERSON><PERSON><PERSON><PERSON>
.\" Modifications for XZ Utils: <PERSON><PERSON>
.\"
.\" License: GNU GPLv2+
.\"
.TH XZMORE 1 "2013-06-30" "Tukaani" "XZ Utils"
.SH NAME
xzmore, lzmore \- view xz or lzma compressed (text) files
.SH SYNOPSIS
.B xzmore
.RI [ file... ]
.br
.B lzmore
.RI [ file... ]
.SH DESCRIPTION
.B  xzmore
is a filter which allows examination of
.BR xz (1)
or
.BR lzma (1)
compressed text files one screenful at a time
on a soft-copy terminal.
.PP
To use a pager other than the default
.B more,
set environment variable
.B PAGER
to the name of the desired program.
The name
.B lzmore
is provided for backward compatibility with LZMA Utils.
.TP
.BR e " or " q
When the prompt \-\-More\-\-(Next file:
.IR file )
is printed, this command causes
.B xzmore
to exit.
.TP
.B s
When the prompt \-\-More\-\-(Next file:
.IR file )
is printed, this command causes
.B xzmore
to skip the next file and continue.
.PP
For list of keyboard commands supported while actually viewing the
content of a file, refer to manual of the pager you use, usually
.BR more (1).
.SH "SEE ALSO"
.BR more (1),
.BR xz (1),
.BR xzless (1),
.BR zmore (1)
