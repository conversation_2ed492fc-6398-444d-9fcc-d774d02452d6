#!/bin/bash
# -*- encoding: utf-8 -*-
# WebDAV上传服务启动脚本
# Author: AI Assistant
# Version: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 配置变量
PYTHON_CMD="python3"
WEBDAV_TASK="webdav_upload_task.py"
COMPRESS_TASK="compress_task.py"
MAIN_TASK="main.py"
PID_DIR="./pids"
LOG_DIR="/data/log"

# 创建必要目录
mkdir -p "$PID_DIR"
mkdir -p "$LOG_DIR"

# 检查Python环境
check_python() {
    if ! command -v $PYTHON_CMD &> /dev/null; then
        if command -v python &> /dev/null; then
            PYTHON_CMD="python"
        else
            log_error "Python未安装或不在PATH中"
            exit 1
        fi
    fi
    log_info "使用Python: $(which $PYTHON_CMD)"
}

# 检查依赖
check_dependencies() {
    log_info "检查Python依赖..."
    
    if [ -f "requirements_webdav.txt" ]; then
        $PYTHON_CMD -c "
import pkg_resources
import sys

required = []
with open('requirements_webdav.txt', 'r') as f:
    for line in f:
        line = line.strip()
        if line and not line.startswith('#'):
            required.append(line)

missing = []
for package in required:
    try:
        pkg_resources.require(package)
    except:
        missing.append(package)

if missing:
    print('缺少依赖包:', ', '.join(missing))
    print('请运行: pip install -r requirements_webdav.txt')
    sys.exit(1)
else:
    print('所有依赖包已安装')
"
        if [ $? -ne 0 ]; then
            log_error "依赖检查失败"
            exit 1
        fi
    fi
}

# 检查Alist服务
check_alist() {
    log_info "检查Alist服务状态..."
    
    if systemctl is-active --quiet alist; then
        log_info "Alist服务运行正常"
    else
        log_warn "Alist服务未运行，尝试启动..."
        if sudo systemctl start alist; then
            sleep 3
            if systemctl is-active --quiet alist; then
                log_info "Alist服务启动成功"
            else
                log_error "Alist服务启动失败"
                exit 1
            fi
        else
            log_error "无法启动Alist服务，请检查安装"
            exit 1
        fi
    fi
}

# 测试WebDAV连接
test_webdav() {
    log_info "测试WebDAV连接..."
    
    $PYTHON_CMD -c "
import sys
sys.path.append('.')
from utils.yaml_handler import read_yaml
from utils.webdav_handler import WebDAVUploader
from utils.log_handler import LOG

try:
    conf_info = read_yaml('conf/config.yaml')
    webdav_config = conf_info.get('webdav')
    
    if not webdav_config:
        print('WebDAV配置不存在')
        sys.exit(1)
    
    log = LOG()
    logger = log.logger
    
    uploader = WebDAVUploader(
        webdav_url=webdav_config.get('url'),
        username=webdav_config.get('username'),
        password=webdav_config.get('password'),
        logger=logger
    )
    
    if uploader.check_connection():
        print('WebDAV连接测试成功')
    else:
        print('WebDAV连接测试失败')
        sys.exit(1)
        
except Exception as e:
    print(f'WebDAV连接测试异常: {e}')
    sys.exit(1)
"
    
    if [ $? -eq 0 ]; then
        log_info "WebDAV连接正常"
    else
        log_error "WebDAV连接失败，请检查配置"
        exit 1
    fi
}

# 启动进程
start_process() {
    local script=$1
    local name=$2
    local pid_file="$PID_DIR/${name}.pid"
    local log_file="$LOG_DIR/${name}.log"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_warn "$name 已在运行 (PID: $pid)"
            return 0
        else
            rm -f "$pid_file"
        fi
    fi
    
    log_info "启动 $name..."
    nohup $PYTHON_CMD "$script" > "$log_file" 2>&1 &
    local pid=$!
    echo $pid > "$pid_file"
    
    # 等待进程启动
    sleep 2
    if kill -0 "$pid" 2>/dev/null; then
        log_info "$name 启动成功 (PID: $pid)"
    else
        log_error "$name 启动失败"
        rm -f "$pid_file"
        return 1
    fi
}

# 停止进程
stop_process() {
    local name=$1
    local pid_file="$PID_DIR/${name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "停止 $name (PID: $pid)..."
            kill "$pid"
            
            # 等待进程结束
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            if kill -0 "$pid" 2>/dev/null; then
                log_warn "强制停止 $name"
                kill -9 "$pid"
            fi
            
            rm -f "$pid_file"
            log_info "$name 已停止"
        else
            log_warn "$name 未运行"
            rm -f "$pid_file"
        fi
    else
        log_warn "$name PID文件不存在"
    fi
}

# 显示状态
show_status() {
    echo "=================================="
    echo "服务状态"
    echo "=================================="
    
    # Alist服务状态
    if systemctl is-active --quiet alist; then
        echo "Alist服务: ✅ 运行中"
    else
        echo "Alist服务: ❌ 未运行"
    fi
    
    # 各个Python进程状态
    for name in main compress webdav_upload; do
        local pid_file="$PID_DIR/${name}.pid"
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                echo "$name: ✅ 运行中 (PID: $pid)"
            else
                echo "$name: ❌ 未运行"
                rm -f "$pid_file"
            fi
        else
            echo "$name: ❌ 未运行"
        fi
    done
    
    echo "=================================="
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            log_info "启动WebDAV日志上传系统..."
            check_python
            check_dependencies
            check_alist
            test_webdav
            
            # 启动各个服务
            start_process "$MAIN_TASK" "main"
            start_process "$COMPRESS_TASK" "compress"
            start_process "$WEBDAV_TASK" "webdav_upload"
            
            log_info "所有服务启动完成"
            show_status
            ;;
            
        stop)
            log_info "停止WebDAV日志上传系统..."
            stop_process "webdav_upload"
            stop_process "compress"
            stop_process "main"
            log_info "所有服务已停止"
            ;;
            
        restart)
            $0 stop
            sleep 2
            $0 start
            ;;
            
        status)
            show_status
            ;;
            
        test)
            log_info "测试WebDAV连接..."
            check_python
            test_webdav
            ;;
            
        *)
            echo "用法: $0 [start|stop|restart|status|test]"
            echo "  start   - 启动所有服务 (默认)"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  status  - 显示服务状态"
            echo "  test    - 测试WebDAV连接"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
