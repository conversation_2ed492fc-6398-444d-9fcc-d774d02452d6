# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# @File       : debug.py.py
# @Time       ：2023/3/7 13:08
# <AUTHOR>
# @version    ：1.0
# Description ：
"""
import os
from aligo import Auth
from aligo import Aligo
os.chdir("." if os.path.dirname(__file__) == "" else os.path.dirname(__file__))


def main():

    Auth._EMAIL_HOST = 'mail.yudns.com'
    Auth._EMAIL_PORT = '25'
    Auth._EMAIL_USER = '<EMAIL>'
    Auth._EMAIL_PASSWORD = 'JIAxing735010'
    ali = Aligo(name="test", email=("<EMAIL>", "test"))
    print(ali.get_user())



if __name__ == "__main__":
    main()
