import os
import time
import requests
import traceback
from aligo import Aligo
from aligo import Auth
from utils.restart_network import restart_net
from utils.function_timeout import set_timeout


@set_timeout(timeout=5 * 60)
def upload_file(file_path: str, logger, email_auth, ali_name, email=None, login_key=None) -> bool:

    """
        上传文件到阿里云盘
        file_path: 本地压缩包的路径
        return:
            True: 上传成功
            False: 上传失败
    """

    # 日志的名字
    zip_file_name = os.path.basename(file_path)
    file_path_group = zip_file_name.split("-")
    # 上传目录
    upload_dir_path = f"logs/{file_path_group[0]}/{file_path_group[1]}-{file_path_group[2]}-{file_path_group[3]}/{file_path_group[4]}"
    # 上传名字
    upload_file_name = zip_file_name

    logger.info(msg=f"上传到阿里云盘的目录为: {upload_dir_path}")
    logger.info(msg=f"上传到阿里云盘的文件名为: {upload_file_name}")

    Auth._EMAIL_HOST = email_auth.get("email_host")
    Auth._EMAIL_PORT = email_auth.get("email_port")
    Auth._EMAIL_USER = email_auth.get("email_user")
    Auth._EMAIL_PASSWORD = email_auth.get("email_password")
    
    try:
        if email:
            if not login_key:
                login_key = "login"
            ali = Aligo(name=ali_name, email=(email, f"{file_path_group[0]}{login_key}"))
        else:
            ali = Aligo(name=ali_name)
        logger.info(msg="阿里云盘登录成功")
    except requests.exceptions.ConnectionError:
        logger.error(msg=f"连接不上阿里云, 重启网卡")
        restart_res = restart_net(restart_net_cmd="systemctl restart network")
        logger.info(msg=f"重启网卡, {restart_res}")
        time.sleep(5)
    except Exception as e:
        logger.error(msg=f"登录失败, 失败原因:\n {traceback.format_exc()}")
        return False
    # 获取上传目录的文件id,不存在会自动创建
    upload_dir_path_id = ali.get_folder_by_path(upload_dir_path, create_folder=True).file_id
    try:
        ali.upload_file(file_path=file_path, parent_file_id=upload_dir_path_id, name=upload_file_name)
        logger.info(msg=f"上传文件 {upload_file_name} 到 {upload_dir_path} 目录 -> 成功")
        return True
    except Exception as e:
        logger.error(msg=f"上传文件 {upload_file_name} 到 {upload_dir_path} 目录 -> 失败, 失败原因: {e}")
        return False


@set_timeout(timeout=1 * 60)
def keep_alive(ali_name, email=None, login_key=None):

    """
        保持不中断
    """
    if email:
        if not login_key:
            login_key = "login"
        ali = Aligo(name=ali_name, email=(email, login_key))
    else:
        ali = Aligo(name=ali_name)
    ali.get_user()


if __name__ == "__main__":

    pass

