# Tcl autoload index file, version 2.0
# This file is generated by the "auto_mkindex" command
# and sourced to set up indexing information for one or
# more commands.  Typically each line is a command that
# sets an element in the auto_index array, where the
# element name is the name of a command and the value is
# a script that loads the command.

set auto_index(arrowSetup) [list source -encoding utf-8 [file join $dir arrow.tcl]]
set auto_index(arrowMove1) [list source -encoding utf-8 [file join $dir arrow.tcl]]
set auto_index(arrowMove2) [list source -encoding utf-8 [file join $dir arrow.tcl]]
set auto_index(arrowMove3) [list source -encoding utf-8 [file join $dir arrow.tcl]]
set auto_index(textLoadFile) [list source -encoding utf-8 [file join $dir search.tcl]]
set auto_index(textSearch) [list source -encoding utf-8 [file join $dir search.tcl]]
set auto_index(textToggle) [list source -encoding utf-8 [file join $dir search.tcl]]
set auto_index(itemEnter) [list source -encoding utf-8 [file join $dir items.tcl]]
set auto_index(itemLeave) [list source -encoding utf-8 [file join $dir items.tcl]]
set auto_index(itemMark) [list source -encoding utf-8 [file join $dir items.tcl]]
set auto_index(itemStroke) [list source -encoding utf-8 [file join $dir items.tcl]]
set auto_index(itemsUnderArea) [list source -encoding utf-8 [file join $dir items.tcl]]
set auto_index(itemStartDrag) [list source -encoding utf-8 [file join $dir items.tcl]]
set auto_index(itemDrag) [list source -encoding utf-8 [file join $dir items.tcl]]
set auto_index(butPress) [list source -encoding utf-8 [file join $dir items.tcl]]
set auto_index(loadDir) [list source -encoding utf-8 [file join $dir image2.tcl]]
set auto_index(loadImage) [list source -encoding utf-8 [file join $dir image2.tcl]]
set auto_index(rulerMkTab) [list source -encoding utf-8 [file join $dir ruler.tcl]]
set auto_index(rulerNewTab) [list source -encoding utf-8 [file join $dir ruler.tcl]]
set auto_index(rulerSelectTab) [list source -encoding utf-8 [file join $dir ruler.tcl]]
set auto_index(rulerMoveTab) [list source -encoding utf-8 [file join $dir ruler.tcl]]
set auto_index(rulerReleaseTab) [list source -encoding utf-8 [file join $dir ruler.tcl]]
set auto_index(mkTextConfig) [list source -encoding utf-8 [file join $dir ctext.tcl]]
set auto_index(textEnter) [list source -encoding utf-8 [file join $dir ctext.tcl]]
set auto_index(textInsert) [list source -encoding utf-8 [file join $dir ctext.tcl]]
set auto_index(textPaste) [list source -encoding utf-8 [file join $dir ctext.tcl]]
set auto_index(textB1Press) [list source -encoding utf-8 [file join $dir ctext.tcl]]
set auto_index(textB1Move) [list source -encoding utf-8 [file join $dir ctext.tcl]]
set auto_index(textBs) [list source -encoding utf-8 [file join $dir ctext.tcl]]
set auto_index(textDel) [list source -encoding utf-8 [file join $dir ctext.tcl]]
set auto_index(bitmapRow) [list source -encoding utf-8 [file join $dir bitmap.tcl]]
set auto_index(scrollEnter) [list source -encoding utf-8 [file join $dir cscroll.tcl]]
set auto_index(scrollLeave) [list source -encoding utf-8 [file join $dir cscroll.tcl]]
set auto_index(scrollButton) [list source -encoding utf-8 [file join $dir cscroll.tcl]]
set auto_index(textWindOn) [list source -encoding utf-8 [file join $dir twind.tcl]]
set auto_index(textWindOff) [list source -encoding utf-8 [file join $dir twind.tcl]]
set auto_index(textWindPlot) [list source -encoding utf-8 [file join $dir twind.tcl]]
set auto_index(embPlotDown) [list source -encoding utf-8 [file join $dir twind.tcl]]
set auto_index(embPlotMove) [list source -encoding utf-8 [file join $dir twind.tcl]]
set auto_index(textWindDel) [list source -encoding utf-8 [file join $dir twind.tcl]]
set auto_index(embDefBg) [list source -encoding utf-8 [file join $dir twind.tcl]]
set auto_index(floorDisplay) [list source -encoding utf-8 [file join $dir floor.tcl]]
set auto_index(newRoom) [list source -encoding utf-8 [file join $dir floor.tcl]]
set auto_index(roomChanged) [list source -encoding utf-8 [file join $dir floor.tcl]]
set auto_index(bg1) [list source -encoding utf-8 [file join $dir floor.tcl]]
set auto_index(bg2) [list source -encoding utf-8 [file join $dir floor.tcl]]
set auto_index(bg3) [list source -encoding utf-8 [file join $dir floor.tcl]]
set auto_index(fg1) [list source -encoding utf-8 [file join $dir floor.tcl]]
set auto_index(fg2) [list source -encoding utf-8 [file join $dir floor.tcl]]
set auto_index(fg3) [list source -encoding utf-8 [file join $dir floor.tcl]]
set auto_index(setWidth) [list source -encoding utf-8 [file join $dir hscale.tcl]]
set auto_index(plotDown) [list source -encoding utf-8 [file join $dir plot.tcl]]
set auto_index(plotMove) [list source -encoding utf-8 [file join $dir plot.tcl]]
set auto_index(puzzleSwitch) [list source -encoding utf-8 [file join $dir puzzle.tcl]]
set auto_index(setHeight) [list source -encoding utf-8 [file join $dir vscale.tcl]]
set auto_index(showMessageBox) [list source -encoding utf-8 [file join $dir msgbox.tcl]]
set auto_index(setColor) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(setColor_helper) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(fileDialog) [list source -encoding utf-8 [file join $dir filebox.tcl]]
