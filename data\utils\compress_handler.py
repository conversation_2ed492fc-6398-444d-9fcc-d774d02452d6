# -*- encoding: utf-8 -*-
'''
@File    :   compress_file_handler.py
@Time    :   2022/06/03 14:19:00
<AUTHOR>   lgx 
@Version :   1.0
@Contact :   <EMAIL>
@desc    :   压缩文件                 
'''

# here put the import lib
import tarfile
import os

def compress(zip_file_path: str, file_list: list):

    """
        功能:
            压缩文件
        参数:
            zip_file_path: 压缩包路径
            file_list: 需要压缩的文件
        返回值:
            None
    """

    zf = tarfile.open(zip_file_path, "w:gz")

    for file_path in file_list:

        zf.add(name=file_path, arcname=os.path.basename(file_path))
        
    zf.close()


if __name__ == "__main__":

    compress_dir = "/Users/<USER>/Desktop/1/logs/2022-06-04/16/jjjj"
    compress("1.tgz", [os.path.join(compress_dir, i) for i in os.listdir(compress_dir)])

