# -*- encoding: utf-8 -*-
'''
@File    :   restart_network.py
@Time    :   2022/06/09 11:34:00
<AUTHOR>   lgx 
@Version :   1.0
@Contact :   <EMAIL>
@desc    :   重启网卡
'''

# here put the import lib
import subprocess


def restart_net(restart_net_cmd):

    """
        功能:
            重启Linux网卡
        参数:
            restart_net_cmd: 重启Linux网卡命令
        返回值:
            执行命令的结果
    """
    
    p = subprocess.run(args=restart_net_cmd, shell=True, capture_output=True, text=True)

    restart_res = p.stdout if p.stdout else p.stderr

    return restart_res.replace("\n", "")






