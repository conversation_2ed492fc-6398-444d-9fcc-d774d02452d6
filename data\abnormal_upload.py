# -*- encoding: utf-8 -*-
'''
@File    :   abnormal.py
@Time    :   2022/06/04 21:58:09
<AUTHOR>   lgx 
@Version :   1.0
@Contact :   <EMAIL>
@desc    :   每一分钟检查一次保存上传失败目录是否文件
             如果存在, 则上传
'''

import os
import time
from threading import Thread
from utils.aligo_handler import upload_file, keep_alive
from utils.yaml_handler import read_yaml
from utils.log_handler import LOG


def fail_file_exist(path):

    """
        检查是否存在上传失败的日志
    """

    return [os.path.join(path, filename) for filename in os.listdir(path)]

def main(conf_info, logger):

    # 上传失败的压缩包保存路径
    upload_fail_path = conf_info.get("log_file").get("upload_fail_path")
    # 使用该用户上传
    fail_upload_user = conf_info.get("aligo").get("fail_upload_user")
    # 登录二维码的发送邮箱
    login_to_email = conf_info.get("aligo").get("email")
    # 防伪字符串
    abnormal_upload_key = conf_info.get("aligo").get("fail_upload_key")
    # 压缩包的格式
    package_type = conf_info.get("log_file").get("package_type")

    if not os.path.exists(upload_fail_path):
        os.makedirs(upload_fail_path, exist_ok=True)
    
    logger.info(msg="上传任务开始")

    fail_file_list = [file_path for file_path in fail_file_exist(path=upload_fail_path) if file_path.endswith(package_type)]
    fail_file_num = len(fail_file_list)

    if fail_file_num != 0:

        logger.warning(msg=f"存在{fail_file_num}个上传失败的日志文件, 开始上传!")

        for file_path in fail_file_list:

            try:

                upload_res = upload_file(file_path=file_path, logger=logger, ali_name=fail_upload_user, email=login_to_email, login_key=abnormal_upload_key)
            
            except TimeoutError:

                logger.error(msg="上传超时")
                continue
            
            if upload_res:

                os.remove(file_path)

    else:

        try:

            keep_alive(ali_name=fail_upload_user, email=login_to_email, login_key=abnormal_upload_key)

        except TimeoutError:

            logger.error(msg="登录超时")

        logger.info(msg="没有上传失败的日志文件")

    logger.info(msg="上传任务结束")


if __name__ == "__main__":

    conf_file_path = os.path.join(".", "conf", "config.yaml")
    conf_info = read_yaml(filename=conf_file_path)
    abnormal_upload_interval = conf_info.get("aligo").get("abnormal_upload_interval")
    out_log_path = conf_info.get("log").get("log_path")

    # 初始化日志对象
    log = LOG()
    logger = log.init_log()
    out_log_name = 'abnormal.log'
    file_handler = log.log_to_file(os.path.join(out_log_path, out_log_name))
    logger.addHandler(file_handler)
    
    while True:

        Thread(target=main, args=(conf_info, logger)).start()
        
        time.sleep(abnormal_upload_interval)
        
