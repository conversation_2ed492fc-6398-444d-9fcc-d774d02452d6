.Dd January 25, 2011
.Dt ffi_prep_cif_var 3
.Sh NAME
.Nm ffi_prep_cif_var
.Nd Prepare a
.Nm ffi_cif
structure for use with
.Nm ffi_call
for variadic functions.
.Sh SYNOPSIS
.In ffi.h
.Ft ffi_status
.Fo ffi_prep_cif_var
.Fa "ffi_cif *cif"
.Fa "ffi_abi abi"
.Fa "unsigned int nfixedargs"
.Fa "unsigned int ntotalargs"
.Fa "ffi_type *rtype"
.Fa "ffi_type **atypes"
.Fc
.Sh DESCRIPTION
The
.Nm ffi_prep_cif_var
function prepares a
.Nm ffi_cif
structure for use with
.Nm ffi_call
for variadic functions.
.Fa abi
specifies a set of calling conventions to use.
.Fa atypes
is an array of
.Fa ntotalargs
pointers to
.Nm ffi_type
structs that describe the data type, size and alignment of each argument.
.Fa rtype
points to an
.Nm ffi_type
that describes the data type, size and alignment of the
return value.
.Fa nfixedargs
must contain the number of fixed (non-variadic) arguments.
Note that to call a non-variadic function
.Nm ffi_prep_cif
must be used.
.Sh RETURN VALUES
Upon successful completion,
.Nm ffi_prep_cif_var
returns
.Nm FFI_OK .
It will return
.Nm FFI_BAD_TYPEDEF
if
.Fa cif
is
.Nm NULL
or
.Fa atypes
or
.Fa rtype
is malformed. If
.Fa abi
does not refer to a valid ABI,
.Nm FFI_BAD_ABI
will be returned. Available ABIs are
defined in
.Nm <ffitarget.h>
.
.Sh SEE ALSO
.Xr ffi 3 ,
.Xr ffi_call 3 ,
.Xr ffi_prep_cif 3
