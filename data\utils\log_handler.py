import sys
import logging
import logging.handlers


class LOG:

    def __init__(self) -> None:

        # 获取日志对象
        self.logger = logging.getLogger(__name__)

        # 设置全局日志输出登记
        self.logger.setLevel(level=logging.DEBUG)

        # 设置日志全局输出格式
        self.log_format_obj = logging.Formatter(
            '%(asctime)s--%(filename)s--%(lineno)s--%(levelname)s--%(message)s')
    
    def log_to_file(self, filename: str, log_level=None, when="D", backup_count=10):

        """
            功能:
                日志输出到文件, 并按时间切割
            参数:
                filename：日志文件路径
                log_level：日志等级, None, INFO, WARNING, ERROR
                when: 按照时间切割 S:秒 M:分 H:时 D:天
                backup_count: 备份日志个数
            返回值:
                None
        """

        time_file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=filename, when=when, backupCount=backup_count)

        time_file_handler.setFormatter(self.log_format_obj)

        log_level_info = {

            "INFO": logging.INFO, 
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR

            }

        if log_level:

            time_file_handler.setLevel(level=log_level_info.get(log_level))
        
        self.logger.addHandler(time_file_handler)

    def log_to_stdout(self, log_level=None):

        """
            功能:
                日志输出到屏幕
            参数:
                log_level：日志等级属性
                log_format: 日志格式对象
        """

        stream_hanler_obj = logging.StreamHandler(sys.stdout)

        stream_hanler_obj.setFormatter(self.log_format_obj)

        if log_level == "INFO":

            stream_hanler_obj.setLevel(level=logging.INFO)
        
        elif log_level == "WARNING":

            stream_hanler_obj.setLevel(level=logging.WARNING)
        
        elif log_level == "ERROR":

            stream_hanler_obj.setLevel(level=logging.ERROR)

        self.logger.addHandler(stream_hanler_obj)

if __name__ == "__main__":

    log = LOG()

    log.log_to_file(filename="test.log", when="S", backup_count=2)

    log.log_to_stdout()

    logger = log.logger

    import time

    while True:

        logger.info(msg=f"test")

        time.sleep(1)