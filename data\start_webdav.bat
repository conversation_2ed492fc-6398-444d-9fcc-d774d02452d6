@echo off
REM WebDAV上传服务启动脚本 - Windows版本
REM Author: AI Assistant
REM Version: 1.0

setlocal enabledelayedexpansion

REM 设置颜色
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 获取脚本目录
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

REM 配置变量
set "PYTHON_CMD=python"
set "WEBDAV_TASK=webdav_upload_task.py"
set "COMPRESS_TASK=compress_task.py"
set "MAIN_TASK=main.py"
set "PID_DIR=.\pids"
set "LOG_DIR=C:\data\log"

REM 创建必要目录
if not exist "%PID_DIR%" mkdir "%PID_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM 日志函数
:log_info
echo %GREEN%[INFO]%NC% %~1
goto :eof

:log_warn
echo %YELLOW%[WARN]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM 检查Python环境
:check_python
python --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Python未安装或不在PATH中"
    pause
    exit /b 1
)
call :log_info "Python环境检查通过"
goto :eof

REM 检查依赖
:check_dependencies
call :log_info "检查Python依赖..."
if exist "requirements_webdav.txt" (
    python -c "import pkg_resources; import sys; required=[]; [required.append(line.strip()) for line in open('requirements_webdav.txt', 'r') if line.strip() and not line.startswith('#')]; missing=[]; [missing.append(pkg) for pkg in required if not pkg_resources.require(pkg) or True]; print('所有依赖包已安装') if not missing else (print('缺少依赖包:', ', '.join(missing)), print('请运行: pip install -r requirements_webdav.txt'), sys.exit(1))" 2>nul
    if errorlevel 1 (
        call :log_error "依赖检查失败，请运行: pip install -r requirements_webdav.txt"
        pause
        exit /b 1
    )
)
goto :eof

REM 测试WebDAV连接
:test_webdav
call :log_info "测试WebDAV连接..."
python -c "import sys; sys.path.append('.'); from utils.yaml_handler import read_yaml; from utils.webdav_handler import WebDAVUploader; from utils.log_handler import LOG; conf_info = read_yaml('conf/config.yaml'); webdav_config = conf_info.get('webdav'); log = LOG(); logger = log.logger; uploader = WebDAVUploader(webdav_url=webdav_config.get('url'), username=webdav_config.get('username'), password=webdav_config.get('password'), logger=logger); print('WebDAV连接测试成功') if uploader.check_connection() else (print('WebDAV连接测试失败'), sys.exit(1))" 2>nul
if errorlevel 1 (
    call :log_error "WebDAV连接失败，请检查Alist服务和配置"
    pause
    exit /b 1
)
call :log_info "WebDAV连接正常"
goto :eof

REM 启动进程
:start_process
set "script=%~1"
set "name=%~2"
set "pid_file=%PID_DIR%\%name%.pid"
set "log_file=%LOG_DIR%\%name%.log"

if exist "%pid_file%" (
    call :log_warn "%name% PID文件已存在，可能正在运行"
)

call :log_info "启动 %name%..."
start /b python "%script%" > "%log_file%" 2>&1
call :log_info "%name% 启动完成"
goto :eof

REM 显示状态
:show_status
echo ==================================
echo 服务状态
echo ==================================
echo 请手动检查以下进程是否运行:
echo - main.py (日志收集)
echo - compress_task.py (日志压缩)
echo - webdav_upload_task.py (WebDAV上传)
echo ==================================
goto :eof

REM 主函数
if "%~1"=="" set "action=start"
if not "%~1"=="" set "action=%~1"

if "%action%"=="start" (
    call :log_info "启动WebDAV日志上传系统..."
    call :check_python
    call :check_dependencies
    call :test_webdav
    
    REM 启动各个服务
    call :start_process "%MAIN_TASK%" "main"
    call :start_process "%COMPRESS_TASK%" "compress"
    call :start_process "%WEBDAV_TASK%" "webdav_upload"
    
    call :log_info "所有服务启动完成"
    call :show_status
    pause
) else if "%action%"=="test" (
    call :log_info "测试WebDAV连接..."
    call :check_python
    call :test_webdav
    pause
) else if "%action%"=="status" (
    call :show_status
    pause
) else (
    echo 用法: %0 [start^|test^|status]
    echo   start  - 启动所有服务 ^(默认^)
    echo   test   - 测试WebDAV连接
    echo   status - 显示服务状态
    pause
)

endlocal
