# -*- encoding: utf-8 -*-
'''
@File    :   process_damon.py
@Time    :   2022/06/05 20:54:50
<AUTHOR>   lgx 
@Version :   1.0
@Contact :   <EMAIL>
@desc    :   守护进程              
'''

# here put the import lib
import psutil
import subprocess

class PROCESS_DAMON:

    def __init__(self, process_name: str, running_env: str, process_path: str):

        """
            功能:
                守护进程
            参数:
                process_name: 进程名
                running_env: 执行进程方法
                process_path: 进程路径
            返回值:
                None
        """

        self.process_name = process_name

        self.running_env = running_env

        self.process_path = process_path

        self.start_cmd = f"{self.running_env} {self.process_name}"

    def start_process(self):

        """
            功能: 启动进程
            参数:
                None
            返回值:
                None
        """

        start_cmd_backgroup = f"nohup {self.start_cmd} &"

        subprocess.Popen(args=start_cmd_backgroup, shell=True, 
            cwd=self.process_path, stdout=subprocess.DEVNULL, 
            stderr=subprocess.DEVNULL)
    
    def find_process_pid(self):

        """
            功能:
                寻找进程的pid
            参数:
                None
            返回值
                pid: 进程的pid, 为None说明没有该进程
        """

        pid = None

        for process in psutil.process_iter():

            try:

                if self.start_cmd == " ".join(process.cmdline()):

                    pid = process.pid
            
            except psutil.NoSuchProcess:

                continue
        
        return pid

    def kill_process(self):

        """
            功能:
                杀掉进程
            参数:
                None
            返回值:
                True: 结束进程成功
                False: 结束进程失败

        """

        pid = self.find_process_pid()

        if pid:

            process = psutil.Process(pid=pid)

            process.kill()

            return True
        
        else:

            return False


if __name__ == "__main__":

    pd = PROCESS_DAMON("warning_email.py", "/data/python3.8/bin/python", "/data/")

    pd.start_process()

    print(pd.find_process_pid())