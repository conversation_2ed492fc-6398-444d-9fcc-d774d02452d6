# -*- encoding: utf-8 -*-
'''
@File    :   get_all_file_by_path.py
@Time    :   2022/06/03 13:51:36
<AUTHOR>   lgx 
@Version :   1.0
@Contact :   <EMAIL>
@desc    :   给一个路径, 获取路径下的所有文件                 
'''

# here put the import lib
import os


def dir_empty(dir_path):
    """
        功能:
            判断目录是否为空
        参数:
            dir_path: 要判断的目录
        返回:
            False: 目录不为空
            True: 目录为空

    """
    try:
        next(os.scandir(dir_path))

        return False

    except StopIteration:

        return True


def find_files(path: str) -> list:
    """ 
        功能:
            查找一个目录下的所有文件
        参数:
            path: 需要查找文件的目录
        返回:
            文件列表
    """

    file_list = []
    for root, dirs, files in os.walk(path, topdown=False):
        for name in files:
            file_abs_path = os.path.join(root, name)
            file_list.append(file_abs_path)
    return file_list


def find_dirs(path: str):
    """
        功能:
            找出所有的目录
        参数
            path: 需要查询目录的路径
        返回:
            目录列表
    """

    dir_list = []

    for root, dirs, files in os.walk(path, topdown=False):

        for name in dirs:

            file_abs_path = os.path.join(root, name)

            if dir_empty(file_abs_path):

                dir_list.append(file_abs_path)

    return dir_list