# -*- encoding: utf-8 -*-
'''
@File    :   webdav_handler.py
@Time    :   2024-01-15 10:00:00
<AUTHOR>   AI Assistant
@Version :   1.0
@Contact :   
@desc    :   WebDAV上传处理模块 - 替代aligo的稳定方案
'''

import os
import time
import traceback
from webdav3.client import Client
from utils.function_timeout import set_timeout


class WebDAVUploader:
    """WebDAV上传器"""
    
    def __init__(self, webdav_url, username, password, logger):
        """
        初始化WebDAV客户端
        :param webdav_url: WebDAV服务器地址 (如: http://localhost:5244/dav)
        :param username: 用户名
        :param password: 密码
        :param logger: 日志对象
        """
        self.logger = logger
        self.webdav_url = webdav_url
        self.username = username
        self.password = password
        
        # WebDAV客户端配置
        self.options = {
            'webdav_hostname': webdav_url,
            'webdav_login': username,
            'webdav_password': password,
            'webdav_timeout': 300,  # 5分钟超时
            'disable_check': True,  # 禁用SSL证书检查（如果是HTTP）
        }
        
        self.client = None
        self._init_client()
    
    def _init_client(self):
        """初始化WebDAV客户端"""
        try:
            self.client = Client(self.options)
            # 测试连接
            self.client.list('/')
            self.logger.info(msg=f"WebDAV客户端初始化成功: {self.webdav_url}")
        except Exception as e:
            self.logger.error(msg=f"WebDAV客户端初始化失败: {e}")
            self.client = None
    
    def _ensure_client(self):
        """确保客户端连接正常"""
        if self.client is None:
            self._init_client()
        
        if self.client is None:
            raise Exception("WebDAV客户端连接失败")
    
    def _create_remote_directory(self, remote_path):
        """创建远程目录（递归创建）"""
        try:
            # 分割路径
            path_parts = remote_path.strip('/').split('/')
            current_path = ''
            
            for part in path_parts:
                current_path += '/' + part
                if not self.client.check(current_path):
                    self.client.mkdir(current_path)
                    self.logger.info(msg=f"创建远程目录: {current_path}")
        except Exception as e:
            self.logger.error(msg=f"创建远程目录失败 {remote_path}: {e}")
            raise
    
    @set_timeout(timeout=10 * 60)  # 10分钟超时
    def upload_file(self, local_file_path, remote_file_path, max_retries=3):
        """
        上传文件到WebDAV服务器
        :param local_file_path: 本地文件路径
        :param remote_file_path: 远程文件路径
        :param max_retries: 最大重试次数
        :return: True/False
        """
        if not os.path.exists(local_file_path):
            self.logger.error(msg=f"本地文件不存在: {local_file_path}")
            return False
        
        # 确保远程路径以/开头
        if not remote_file_path.startswith('/'):
            remote_file_path = '/' + remote_file_path
        
        # 创建远程目录
        remote_dir = os.path.dirname(remote_file_path)
        if remote_dir and remote_dir != '/':
            self._create_remote_directory(remote_dir)
        
        retry_count = 0
        while retry_count < max_retries:
            try:
                self._ensure_client()
                
                # 获取文件大小用于日志
                file_size = os.path.getsize(local_file_path)
                self.logger.info(msg=f"开始上传文件: {local_file_path} -> {remote_file_path} ({file_size} bytes)")
                
                # 上传文件
                start_time = time.time()
                self.client.upload_sync(remote_path=remote_file_path, local_path=local_file_path)
                
                # 验证上传是否成功
                if self.client.check(remote_file_path):
                    upload_time = time.time() - start_time
                    self.logger.info(msg=f"文件上传成功: {remote_file_path} (耗时: {upload_time:.2f}秒)")
                    return True
                else:
                    self.logger.error(msg=f"文件上传后验证失败: {remote_file_path}")
                    
            except Exception as e:
                retry_count += 1
                self.logger.error(msg=f"文件上传失败 (第{retry_count}次重试): {e}")
                
                if retry_count < max_retries:
                    # 重新初始化客户端
                    self.client = None
                    time.sleep(5)  # 等待5秒后重试
                else:
                    self.logger.error(msg=f"文件上传最终失败: {traceback.format_exc()}")
        
        return False
    
    def check_connection(self):
        """检查WebDAV连接状态"""
        try:
            self._ensure_client()
            self.client.list('/')
            return True
        except Exception as e:
            self.logger.error(msg=f"WebDAV连接检查失败: {e}")
            return False
    
    def list_files(self, remote_path='/'):
        """列出远程目录文件"""
        try:
            self._ensure_client()
            return self.client.list(remote_path)
        except Exception as e:
            self.logger.error(msg=f"列出远程文件失败: {e}")
            return []
    
    def delete_file(self, remote_path):
        """删除远程文件"""
        try:
            self._ensure_client()
            self.client.clean(remote_path)
            self.logger.info(msg=f"删除远程文件成功: {remote_path}")
            return True
        except Exception as e:
            self.logger.error(msg=f"删除远程文件失败: {e}")
            return False


@set_timeout(timeout=10 * 60)
def upload_file(file_path: str, logger, webdav_config) -> bool:
    """
    上传文件到WebDAV服务器 - 兼容原aligo接口
    :param file_path: 本地压缩包路径
    :param logger: 日志对象
    :param webdav_config: WebDAV配置字典
    :return: True/False
    """
    try:
        # 解析文件名和路径
        zip_file_name = os.path.basename(file_path)
        file_path_group = zip_file_name.split("-")
        
        if len(file_path_group) < 5:
            logger.error(msg=f"文件名格式不正确: {zip_file_name}")
            return False
        
        # 构建远程路径: /logs/hostname/date-hour/cycle.tgz
        remote_path = f"/logs/{file_path_group[0]}/{file_path_group[1]}-{file_path_group[2]}-{file_path_group[3]}/{zip_file_name}"
        
        logger.info(msg=f"WebDAV上传目标路径: {remote_path}")
        
        # 创建WebDAV上传器
        uploader = WebDAVUploader(
            webdav_url=webdav_config.get('url'),
            username=webdav_config.get('username'),
            password=webdav_config.get('password'),
            logger=logger
        )
        
        # 上传文件
        return uploader.upload_file(file_path, remote_path)
        
    except Exception as e:
        logger.error(msg=f"WebDAV上传异常: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    # 测试代码
    import sys
    sys.path.append('..')
    from log_handler import LOG
    
    # 初始化日志
    log = LOG()
    logger = log.logger
    
    # WebDAV配置
    config = {
        'url': 'http://localhost:5244/dav',
        'username': 'admin',
        'password': 'password'
    }
    
    # 测试连接
    uploader = WebDAVUploader(
        webdav_url=config['url'],
        username=config['username'],
        password=config['password'],
        logger=logger
    )
    
    if uploader.check_connection():
        print("WebDAV连接测试成功")
    else:
        print("WebDAV连接测试失败")
