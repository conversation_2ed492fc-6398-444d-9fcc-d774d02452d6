#!/bin/bash
# -*- encoding: utf-8 -*-
# Alist部署脚本 - 用于WebDAV服务
# Author: AI Assistant
# Version: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 配置变量
ALIST_VERSION="v3.37.4"
ALIST_DIR="/opt/alist"
ALIST_CONFIG_DIR="/opt/alist/data"
ALIST_USER="alist"
SERVICE_NAME="alist"

# 检查系统架构
get_arch() {
    case $(uname -m) in
        x86_64)
            echo "amd64"
            ;;
        aarch64)
            echo "arm64"
            ;;
        armv7l)
            echo "armv7"
            ;;
        *)
            log_error "不支持的系统架构: $(uname -m)"
            exit 1
            ;;
    esac
}

# 检查操作系统
get_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "darwin"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 创建alist用户
create_user() {
    log_step "创建alist用户"
    if ! id "$ALIST_USER" &>/dev/null; then
        useradd -r -s /bin/false -d "$ALIST_DIR" "$ALIST_USER"
        log_info "用户 $ALIST_USER 创建成功"
    else
        log_info "用户 $ALIST_USER 已存在"
    fi
}

# 下载并安装Alist
install_alist() {
    log_step "下载并安装Alist"
    
    local os=$(get_os)
    local arch=$(get_arch)
    local download_url="https://github.com/alist-org/alist/releases/download/${ALIST_VERSION}/alist-${os}-${arch}.tar.gz"
    
    log_info "下载地址: $download_url"
    
    # 创建目录
    mkdir -p "$ALIST_DIR"
    mkdir -p "$ALIST_CONFIG_DIR"
    
    # 下载文件
    cd /tmp
    wget -O alist.tar.gz "$download_url" || {
        log_error "下载失败，请检查网络连接"
        exit 1
    }
    
    # 解压并安装
    tar -xzf alist.tar.gz
    mv alist "$ALIST_DIR/"
    chmod +x "$ALIST_DIR/alist"
    
    # 设置权限
    chown -R "$ALIST_USER:$ALIST_USER" "$ALIST_DIR"
    
    log_info "Alist安装完成"
}

# 创建systemd服务文件
create_service() {
    log_step "创建systemd服务"
    
    cat > "/etc/systemd/system/${SERVICE_NAME}.service" << EOF
[Unit]
Description=Alist service
Wants=network.target
After=network.target network.service

[Service]
Type=simple
User=$ALIST_USER
Group=$ALIST_USER
WorkingDirectory=$ALIST_DIR
ExecStart=$ALIST_DIR/alist server --data $ALIST_CONFIG_DIR
ExecReload=/bin/kill -HUP \$MAINPID
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    log_info "systemd服务创建完成"
}

# 初始化Alist配置
init_config() {
    log_step "初始化Alist配置"
    
    # 切换到alist用户执行初始化
    sudo -u "$ALIST_USER" "$ALIST_DIR/alist" admin set admin123456 --data "$ALIST_CONFIG_DIR"
    
    log_info "默认管理员密码已设置为: admin123456"
    log_warn "请登录后及时修改密码！"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙"
    
    if command -v ufw &> /dev/null; then
        ufw allow 5244/tcp
        log_info "UFW防火墙规则已添加"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port=5244/tcp
        firewall-cmd --reload
        log_info "firewalld防火墙规则已添加"
    else
        log_warn "未检测到防火墙，请手动开放5244端口"
    fi
}

# 启动服务
start_service() {
    log_step "启动Alist服务"
    
    systemctl enable "$SERVICE_NAME"
    systemctl start "$SERVICE_NAME"
    
    # 等待服务启动
    sleep 3
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "Alist服务启动成功"
    else
        log_error "Alist服务启动失败"
        systemctl status "$SERVICE_NAME"
        exit 1
    fi
}

# 显示安装信息
show_info() {
    log_step "安装完成信息"
    
    echo
    echo "=================================="
    echo "Alist安装完成！"
    echo "=================================="
    echo "访问地址: http://$(hostname -I | awk '{print $1}'):5244"
    echo "管理员账号: admin"
    echo "管理员密码: admin123456"
    echo "WebDAV地址: http://$(hostname -I | awk '{print $1}'):5244/dav"
    echo "配置目录: $ALIST_CONFIG_DIR"
    echo "日志查看: journalctl -u $SERVICE_NAME -f"
    echo "=================================="
    echo
    log_warn "请及时登录管理界面修改默认密码！"
    log_info "配置阿里云盘后，WebDAV地址可用于上传文件"
}

# 主函数
main() {
    log_info "开始安装Alist..."
    
    check_root
    create_user
    install_alist
    create_service
    init_config
    configure_firewall
    start_service
    show_info
    
    log_info "Alist安装完成！"
}

# 卸载函数
uninstall() {
    log_step "卸载Alist"
    
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    systemctl disable "$SERVICE_NAME" 2>/dev/null || true
    rm -f "/etc/systemd/system/${SERVICE_NAME}.service"
    systemctl daemon-reload
    
    rm -rf "$ALIST_DIR"
    userdel "$ALIST_USER" 2>/dev/null || true
    
    log_info "Alist卸载完成"
}

# 检查参数
case "${1:-install}" in
    install)
        main
        ;;
    uninstall)
        uninstall
        ;;
    *)
        echo "用法: $0 [install|uninstall]"
        echo "  install   - 安装Alist (默认)"
        echo "  uninstall - 卸载Alist"
        exit 1
        ;;
esac
