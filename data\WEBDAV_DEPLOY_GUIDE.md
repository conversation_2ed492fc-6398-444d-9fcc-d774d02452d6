# Alist + WebDAV 部署指南

## 🎯 方案概述

本方案使用 **Alist + WebDAV** 替代原有的 aligo 接口，提供更稳定的阿里云盘上传服务。

### 优势对比

| 特性 | aligo方案 | Alist+WebDAV方案 |
|------|-----------|------------------|
| 稳定性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 维护成本 | 高 | 低 |
| 登录复杂度 | 复杂(二维码) | 简单(用户名密码) |
| API依赖 | 非官方API | 标准WebDAV协议 |
| 扩展性 | 仅阿里云盘 | 支持多种云盘 |

## 🚀 快速部署

### 第一步：安装Alist服务

```bash
# 给脚本执行权限
chmod +x alist_deploy.sh

# 运行安装脚本 (需要root权限)
sudo ./alist_deploy.sh install
```

安装完成后会显示：
- 访问地址: http://YOUR_IP:5244
- 管理员账号: admin
- 管理员密码: admin123456
- WebDAV地址: http://YOUR_IP:5244/dav

### 第二步：配置阿里云盘存储

```bash
# 安装Python依赖
pip install -r requirements_webdav.txt

# 运行配置脚本
python alist_config.py
```

按照提示：
1. 输入Alist地址和管理员账号
2. 获取阿里云盘refresh_token
3. 配置挂载路径

### 第三步：更新系统配置

编辑 `conf/config.yaml`，更新WebDAV配置：

```yaml
webdav:
  url: "http://localhost:5244/dav"
  username: "admin"
  password: "your_new_password"  # 请修改默认密码
  upload_fail_times: 3
  timeout: 300
  verify_ssl: false
```

### 第四步：启动新的上传服务

```bash
# 停止原有的上传服务
python stop.sh

# 启动WebDAV上传服务
python webdav_upload_task.py
```

## 🔧 详细配置说明

### Alist管理界面配置

1. **访问管理界面**: http://YOUR_IP:5244
2. **登录**: admin / admin123456
3. **修改密码**: 设置 → 个人资料 → 修改密码
4. **添加存储**: 存储 → 添加 → 阿里云盘Open

### 阿里云盘refresh_token获取

1. 访问: https://alist.nn.ci/tool/aliyundrive/request
2. 使用阿里云盘APP扫码登录
3. 复制获取到的refresh_token
4. 在Alist中配置存储时使用

### WebDAV客户端测试

```bash
# 使用curl测试WebDAV连接
curl -u admin:password http://localhost:5244/dav/

# 上传文件测试
curl -u admin:password -T test.txt http://localhost:5244/dav/test.txt
```

## 📁 目录结构说明

```
data/
├── utils/
│   └── webdav_handler.py          # WebDAV上传处理模块
├── conf/
│   └── config.yaml                # 更新后的配置文件
├── webdav_upload_task.py          # 新的上传任务
├── alist_deploy.sh                # Alist部署脚本
├── alist_config.py                # Alist配置脚本
├── requirements_webdav.txt        # WebDAV方案依赖
└── WEBDAV_DEPLOY_GUIDE.md         # 本部署指南
```

## 🔄 服务管理

### Alist服务管理

```bash
# 启动服务
sudo systemctl start alist

# 停止服务
sudo systemctl stop alist

# 重启服务
sudo systemctl restart alist

# 查看状态
sudo systemctl status alist

# 查看日志
sudo journalctl -u alist -f
```

### 上传任务管理

```bash
# 启动WebDAV上传任务
python webdav_upload_task.py

# 后台运行
nohup python webdav_upload_task.py > webdav_upload.log 2>&1 &

# 查看上传日志
tail -f /data/log/webdav_upload_task.log
```

## 🛠️ 故障排除

### 常见问题

1. **Alist无法启动**
   - 检查端口5244是否被占用: `netstat -tlnp | grep 5244`
   - 查看服务日志: `journalctl -u alist -f`

2. **WebDAV连接失败**
   - 检查Alist服务状态
   - 验证用户名密码是否正确
   - 确认防火墙已开放5244端口

3. **阿里云盘refresh_token失效**
   - 重新获取refresh_token
   - 在Alist管理界面更新存储配置

4. **上传失败**
   - 检查网络连接
   - 查看WebDAV上传日志
   - 验证目标路径权限

### 日志位置

- Alist服务日志: `journalctl -u alist -f`
- WebDAV上传日志: `/data/log/webdav_upload_task.log`
- 系统日志: `/var/log/syslog`

## 🔒 安全建议

1. **修改默认密码**: 及时修改Alist管理员密码
2. **防火墙配置**: 仅开放必要端口
3. **HTTPS配置**: 生产环境建议配置SSL证书
4. **访问控制**: 限制管理界面访问IP

## 📈 性能优化

1. **并发上传**: 可在配置中调整上传线程数
2. **缓存设置**: 适当调整Alist缓存过期时间
3. **网络优化**: 使用CDN加速访问
4. **存储优化**: 定期清理失败的上传文件

## 🔄 迁移方案

### 从aligo迁移到WebDAV

1. **备份现有配置**: 备份 `conf/config.yaml`
2. **部署Alist服务**: 按照上述步骤部署
3. **更新配置文件**: 添加WebDAV配置
4. **测试连接**: 确保WebDAV连接正常
5. **切换服务**: 停止aligo服务，启动WebDAV服务
6. **监控运行**: 观察上传任务运行状态

### 回滚方案

如需回滚到aligo方案：

1. 停止WebDAV上传服务
2. 恢复原始配置文件
3. 启动原有的upload_task.py
4. 可选择保留Alist服务作为备用

## 📞 技术支持

如遇到问题，请检查：
1. 系统日志和服务状态
2. 网络连接和防火墙设置
3. 配置文件格式和参数
4. 阿里云盘token有效性

---

**注意**: 首次部署建议在测试环境验证后再应用到生产环境。
