# -*- encoding: utf-8 -*-
'''
@File    :   run.py
@Time    :   2022/06/04 11:41:08
<AUTHOR>   lgx 
@Version :   1.0
@Contact :   <EMAIL>
@desc    :   压缩生成的日志,
             然后上传阿里云
'''

import os
import time
import shutil

from utils.yaml_handler import read_yaml
from utils.log_handler import LOG
from utils.file_handler import *
from utils.aligo_handler import upload_file
from utils.compress_handler_old import compress


def main(conf_info, logger):

    # 生成日志文件的根目录
    save_log_path = conf_info.get("log_file").get("log_path")

    # 保存压缩日志文件的路径
    save_rar_path = conf_info.get("log_file").get("rar_path")

    # 上传失败重试次数
    upload_fail_retry_times = conf_info.get("aligo").get("upload_fail_times")

    # 保存上传失败日志的目录
    upload_fail_path = conf_info.get("log_file").get("upload_fail_path")

    # 上传正常日志文件的用户
    ali_user_name = conf_info.get("aligo").get("normal_upload_user")

    # 登录二维码的发送邮箱
    login_to_email = conf_info.get("aligo").get("email")

    # 防伪字符串
    normal_upload_key = conf_info.get("aligo").get("normal_upload_key")

    # 压缩包的格式
    package_type = conf_info.get("log_file").get("package_type")

    # 文件周期时间
    log_file_cycle = conf_info.get("log_file").get("log_file_cycle")

    # 标准的日志文件大小
    std_log_file_size = conf_info.get("log_file").get("std_log_file_size")


    if (log_file_cycle / 5) == 0:

        print("文件周期时间必须为5的倍数, 脚本退出")

        exit(1)
    
    if not os.path.exists(save_rar_path):

        os.makedirs(save_rar_path, exist_ok=True)
    
    if not os.path.exists(upload_fail_path):

        os.makedirs(upload_fail_path, exist_ok=True)

    logger.info(msg="任务开始")

    if not os.path.exists(path=save_log_path):

        logger.error(msg=f"生成日志的根目录:{save_log_path} 不存在, 此次任务结束!")

        return 1

    if not os.path.exists(path=save_rar_path):

        os.makedirs(name=save_rar_path, exist_ok=True)
 
    # 对所有的日志文件进行排序
    log_file_list_order = sorted(find_files(path=save_log_path), 
        key=lambda x: os.path.getmtime(filename=x))
    
    log_file_list_match = []

    # 对日志文件进行校验, 文件名应该为整数且大小要大于设置的标准大小
    for file_path in log_file_list_order[:-1]:

        if os.path.getsize(file_path) > std_log_file_size and \
                os.path.basename(file_path).isdigit():

            log_file_list_match.append(file_path)
        
        else:

            os.remove(file_path)
    
    if not log_file_list_match:

        logger.error(msg=f"没有匹配到文件, 此次任务结束!")
       
        time.sleep(60) 
       
        return 1
    
    # 第一个日志文件的名字
    first_file_name = os.path.basename(log_file_list_match[0]) 

    # # 第一个日志文件的路径
    first_file_parent_path = os.path.dirname(log_file_list_order[0])

    # 需要上传的日志文件列表
    upload_log_list = []

    # 处于第几个周期
    cycle_num = int(int(first_file_name) / log_file_cycle)

    # 处于周期的第几个文件
    file_seq = int(first_file_name) % log_file_cycle

    logger.info(msg=f"最早的文件为: {first_file_name}, 属于第 {cycle_num + 1} 个周期的第 {file_seq + 1} 个文件, 需要上传的文件: 从 {first_file_name} 到 {(cycle_num + 1) * log_file_cycle - 1 }")
        
    for i in range(int(first_file_name), (cycle_num + 1) * log_file_cycle):
        
        if i < 10:

            filename = f"0{i}"

        else:

            filename = str(i)
        
        upload_log_path = os.path.join(first_file_parent_path, filename)

        wait_times = 0

        while wait_times < 3:
        
            if os.path.exists(upload_log_path):

                upload_log_list.append(upload_log_path)

                logger.info(msg=f"{filename} 文件已经生成")

                break

            else:

                logger.info(msg=f"{filename} 文件没有生成, 等待1分钟后检查...")

                time.sleep(60)
            
            wait_times += 1
        
    logger.info(msg=f"需要上传的文件生成完毕, 等待该周期最后一个文件写入完成...")
    
    while True:

        file_size_before = os.path.getsize(upload_log_list[-1])
        
        time.sleep(30)

        file_size_after = os.path.getsize(upload_log_list[-1])

        if file_size_before == file_size_after:

            logger.info(msg=f"需要上传的文件生成完毕, 该周期最后一个文件写入完成...")

            break

    host_name = upload_log_list[0].split("/")[-2]

    file_date = upload_log_list[0].split("/")[-4]

    file_hour = upload_log_list[0].split("/")[-3]

    logger.info(msg=f"获取到 主机名: {host_name}, 日期:{file_date}, 小时: {file_hour},\
        周期开始的文件名: {cycle_num * log_file_cycle}, 周期结束的文件名: {(cycle_num + 1) * log_file_cycle}\
        实际开始文件名: {os.path.basename(upload_log_list[0])}, 实际结束文件名: {os.path.basename(upload_log_list[-1])}")

    with open("hostname", "w") as f:
        
        f.write(host_name)

    log_rar_path = os.path.join(save_rar_path, f'{host_name}-{file_date}-{file_hour}-{(cycle_num + 1) * log_file_cycle}{package_type}')
    
    logger.info(msg=f"需要上传压缩包的路径为: {log_rar_path}")

    logger.info("压缩中, 请等待...")
    
    compress(zip_file_path=log_rar_path, file_list=upload_log_list)
    
    logger.info(msg=f"压缩完成!")

    retry_times = 0

    upload_flag = False

    while retry_times < upload_fail_retry_times:
        
        logger.info(msg=f"尝试第{retry_times + 1 }次上传压缩包 {log_rar_path}")

        try:

            upload_res = upload_file(file_path=log_rar_path, logger=logger, ali_name=ali_user_name, email=login_to_email, login_key=normal_upload_key)
        
        except TimeoutError:

            logger.error(msg=f"上传超时, 跳过此次上传!")

            retry_times += 1

            continue

        if upload_res:

            os.remove(log_rar_path)
            
            logger.info(msg=f" 压缩包 {log_rar_path} 上传成功, 删掉压缩包")
            
            for file_path in upload_log_list:

                os.remove(path=file_path)

            logger.info(msg=f"删除上传好的日志文件")
            
            for dir_path in find_dirs(path=save_log_path):

                try:
                    os.removedirs(name=dir_path)
                except:
                    pass

            logger.info(msg=f"删除空文件")

            upload_flag = True

            break

        else:

            logger.error(msg=f"第{retry_times + 1 }次上传压缩包 {log_rar_path} 失败!")
            retry_times += 1
            time.sleep(60 * 1)
    
    if not upload_flag:

        logger.error(msg=f"日志文件 {log_rar_path} 上传 {upload_fail_retry_times} 次都失败了!")

        shutil.move(src=log_rar_path, dst=upload_fail_path)

        logger.info(f"把上传失败的日志压缩包 {log_rar_path} 移动到 {upload_fail_path}")

    
    logger.info(msg="任务结束")


if __name__ == "__main__":

    # 配置文件的路径
    conf_file_path = os.path.join(".", "conf", "config.yaml")

    # 读取配置信息
    conf_info = read_yaml(filename=conf_file_path)

    # 脚本日志的保存路径
    out_log_path = conf_info.get("log").get("log_path")

    normal_upload_interval = conf_info.get("aligo").get("normal_upload_interval")
    
    # 日志清理
    for old_log_file in find_files(out_log_path):

        if os.path.getsize(old_log_file) > (10 * 1024 * 1024):

            os.remove(old_log_file)

    ### 初始化脚本日志的对象 BEGIN ###
    log = LOG()

    logger = log.init_log()
    
    # 脚本日志的名字
    out_log_name = 'normal.log'

    # 初始化日志文件流
    file_handler = log.log_to_file(os.path.join(out_log_path, out_log_name))

    # 把日志文件流加入日志对象中以便日志输出到文件
    logger.addHandler(file_handler)

    ### 初始化脚本日志的对象 END ###
    
    while True:

        main(conf_info, logger)
