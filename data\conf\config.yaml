﻿log_file:
  log_path: "/dev/shm/logs"
  rar_path: "/dev/shm/log"
  upload_fail_path: "/root/error"
  rar_type: ".tgz"
  log_file_cycle: 5
  std_log_file_size: 10485760
log:
  log_path: "/data/log"  
# WebDAV配置 - 替代aligo的稳定方案
webdav:
  # Alist WebDAV服务地址
  url: "http://localhost:5244/dav"
  # Alist管理员账号
  username: "admin"
  # Alist管理员密码
  password: "your_alist_password"
  # 上传失败重试次数
  upload_fail_times: 3
  # 连接超时时间(秒)
  timeout: 300
  # 是否启用SSL验证
  verify_ssl: false

# 保留原aligo配置作为备用方案
aligo:
  abnormal_upload_interval: 300
  upload_fail_times: 3
  normal_upload_user: "normal"
  fail_upload_user: "fail"
  email: "<EMAIL>"
  normal_upload_key: "正常上传的登录二维码"
  fail_upload_key: "失败上传的登录二维码"
  auth:
    email:
      email_host: "mail.yudns.com"
      email_port: 25
      email_user: "<EMAIL>"
      email_password: "JIAxing735010"
email:
  check_interval: 300
  sender: "<EMAIL>"
  smtp: "smtp.qq.com"
  auth_code: "bylfukkvcqdbbjbj"
  receiver: "<EMAIL>"
  subject: "hostname存在上传失败的日志压缩包"
  content: "有rar_file_count个日志压缩包! 请及时处理"
