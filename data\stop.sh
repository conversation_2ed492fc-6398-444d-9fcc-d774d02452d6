#!/bin/bash
#
# 停止run_damon.py normal_upload.py warning_email.py abnormal_upload.py main.py 进程
#


# 先停止每分钟检查守护进程是否存在的定时任务
sed -i 's|^\*/1 \* \* \* \* sh /data/self_damon.sh$|#\*/1 \* \* \* \* sh /data/self_damon.sh|' /var/spool/cron/root
echo "停止每分钟检查守护进程是否存在的定时任务"

# 进程数组
process_name_list[0]="python main.py"
process_name_list[1]="/data/python3.8/bin/python compress_task.py"
process_name_list[2]="/data/python3.8/bin/python upload_task.py"
process_name_list[3]="/data/python3.8/bin/python warning_email.py"
process_name_list[4]="/data/python3.8/bin/python damon_task.py"


# 停止进程
for (( i=0; i<${#process_name_list[@]};i++ ))
do

    if [ -z `ps -ef | grep -v grep | grep "${process_name_list[i]}" | awk '{print $2}'` ];then

         echo "${process_name_list[i]} 已停止"

    else

        echo "${process_name_list[i]} 存在, 停止该进程"
        ps -ef | grep -v grep | grep "${process_name_list[i]}" | awk '{print $2}' | xargs kill -9
    
    fi

done
