# -*- encoding: utf-8 -*-
'''
@File    :   function_timeout.py
@Time    :   2022/07/05 11:18:44
<AUTHOR>   lgx 
@Version :   1.0
@Contact :   <EMAIL>
@desc    :   给函数加上超时操作
'''

# here put the import lib

import signal
import time
 
 
def set_timeout(timeout):

    """
        功能: 参数函数装饰器
        参数:
            timeout: 超时阈值,单位为秒
            callback: 超时之后回调的函数
        返回值:
            wrap: 返回装饰后的函数
    """

    def wrap(func):

        # 收到信号 SIGALRM 后的回调函数，第一个参数是信号的数字，
        # 第二个参数是the interrupted stack frame.

        def handle(signum, frame): 

            raise TimeoutError


        def to_do(*args, **kwargs):

            # 设置信号和回调函数
            signal.signal(signal.SIGALRM, handle)
            # 设置 num 秒的闹钟 
            signal.alarm(timeout)

            r = func(*args, **kwargs)

            # 关闭闹钟
            signal.alarm(0) 
            
            return r

        return to_do

    return wrap
 
 
if __name__ == '__main__':

    @set_timeout(2) # 限时 2 秒
    def connect(): # 要执行的函数

        time.sleep(3) # 函数执行时间，写大于2的值，可测试超时

        return 'connect success.'
 
    try: 

        print(connect())

    except TimeoutError:

        print("执行超时")

