# -*- encoding: utf-8 -*-
'''
@File    :   alist_config.py
@Time    :   2024-01-15 10:00:00
<AUTHOR>   AI Assistant
@Version :   1.0
@Contact :   
@desc    :   Alist配置脚本 - 自动配置阿里云盘存储
'''

import requests
import json
import time
import sys
from typing import Dict, Any


class AlistConfig:
    """Alist配置管理器"""
    
    def __init__(self, base_url: str = "http://localhost:5244", username: str = "admin", password: str = "admin123456"):
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.token = None
        self.session = requests.Session()
        
    def login(self) -> bool:
        """登录Alist管理界面"""
        login_url = f"{self.base_url}/api/auth/login"
        login_data = {
            "username": self.username,
            "password": self.password
        }
        
        try:
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                self.token = result.get("data", {}).get("token")
                self.session.headers.update({"Authorization": f"Bearer {self.token}"})
                print(f"✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {result.get('message', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_storages(self) -> list:
        """获取存储列表"""
        try:
            response = self.session.get(f"{self.base_url}/api/admin/storage/list")
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                return result.get("data", {}).get("content", [])
            else:
                print(f"❌ 获取存储列表失败: {result.get('message')}")
                return []
                
        except Exception as e:
            print(f"❌ 获取存储列表异常: {e}")
            return []
    
    def add_aliyundrive_storage(self, mount_path: str, refresh_token: str, root_folder_id: str = "root") -> bool:
        """添加阿里云盘存储"""
        storage_data = {
            "mount_path": mount_path,
            "order": 0,
            "driver": "AliyundriveOpen",
            "cache_expiration": 30,
            "status": "work",
            "addition": json.dumps({
                "refresh_token": refresh_token,
                "root_folder_id": root_folder_id,
                "order_by": "name",
                "order_direction": "ASC"
            }),
            "remark": "阿里云盘存储",
            "modified": int(time.time()),
            "disabled": False,
            "enable_sign": False,
            "order_by": "",
            "order_direction": "",
            "extract_folder": "",
            "web_proxy": False,
            "webdav_policy": "native_proxy",
            "down_proxy_url": ""
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/admin/storage/create", json=storage_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                print(f"✅ 阿里云盘存储添加成功: {mount_path}")
                return True
            else:
                print(f"❌ 添加阿里云盘存储失败: {result.get('message')}")
                return False
                
        except Exception as e:
            print(f"❌ 添加阿里云盘存储异常: {e}")
            return False
    
    def update_settings(self, settings: Dict[str, Any]) -> bool:
        """更新Alist设置"""
        try:
            response = self.session.post(f"{self.base_url}/api/admin/setting/save", json=settings)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                print("✅ 设置更新成功")
                return True
            else:
                print(f"❌ 设置更新失败: {result.get('message')}")
                return False
                
        except Exception as e:
            print(f"❌ 设置更新异常: {e}")
            return False
    
    def enable_webdav(self) -> bool:
        """启用WebDAV功能"""
        settings = [
            {"key": "webdav", "value": "true", "type": "bool", "group": 5, "flag": 0}
        ]
        return self.update_settings(settings)
    
    def check_connection(self) -> bool:
        """检查Alist连接"""
        try:
            response = self.session.get(f"{self.base_url}/ping", timeout=5)
            return response.status_code == 200
        except:
            return False


def main():
    """主配置流程"""
    print("🚀 Alist自动配置脚本")
    print("=" * 50)
    
    # 获取配置参数
    base_url = input("请输入Alist地址 (默认: http://localhost:5244): ").strip() or "http://localhost:5244"
    username = input("请输入管理员用户名 (默认: admin): ").strip() or "admin"
    password = input("请输入管理员密码 (默认: admin123456): ").strip() or "admin123456"
    
    # 创建配置器
    config = AlistConfig(base_url, username, password)
    
    # 检查连接
    print("\n🔍 检查Alist连接...")
    if not config.check_connection():
        print("❌ 无法连接到Alist服务，请检查服务是否正常运行")
        sys.exit(1)
    
    # 登录
    print("\n🔐 登录Alist管理界面...")
    if not config.login():
        print("❌ 登录失败，请检查用户名和密码")
        sys.exit(1)
    
    # 启用WebDAV
    print("\n🌐 启用WebDAV功能...")
    config.enable_webdav()
    
    # 配置阿里云盘存储
    print("\n☁️  配置阿里云盘存储...")
    print("请按照以下步骤获取refresh_token:")
    print("1. 打开浏览器访问: https://alist.nn.ci/tool/aliyundrive/request")
    print("2. 使用阿里云盘APP扫码登录")
    print("3. 复制获取到的refresh_token")
    print()
    
    refresh_token = input("请输入阿里云盘refresh_token: ").strip()
    if not refresh_token:
        print("❌ refresh_token不能为空")
        sys.exit(1)
    
    mount_path = input("请输入挂载路径 (默认: /aliyundrive): ").strip() or "/aliyundrive"
    
    # 添加存储
    if config.add_aliyundrive_storage(mount_path, refresh_token):
        print(f"\n✅ 配置完成！")
        print("=" * 50)
        print(f"WebDAV地址: {base_url}/dav")
        print(f"用户名: {username}")
        print(f"密码: {password}")
        print(f"阿里云盘挂载路径: {mount_path}")
        print("=" * 50)
        print("\n📝 请更新config.yaml中的WebDAV配置:")
        print(f"webdav:")
        print(f"  url: \"{base_url}/dav\"")
        print(f"  username: \"{username}\"")
        print(f"  password: \"{password}\"")
    else:
        print("❌ 配置失败")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 配置已取消")
    except Exception as e:
        print(f"\n❌ 配置过程中出现异常: {e}")
        sys.exit(1)
